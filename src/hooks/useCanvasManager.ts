import { useState, useCallback, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { Canvas, CanvasManagerData, MindMapData, NodeData, ViewportState } from '../types';
import { DEFAULT_MODEL } from '../config/models';

const CANVAS_MANAGER_STORAGE_KEY = 'canvas-manager-data';

const createInitialNode = (x: number = 400, y: number = 300, searchGrounding: boolean = false): NodeData => ({
  id: uuidv4(),
  x,
  y,
  title: 'Main Topic',
  query: undefined,
  response: undefined,
  sources: undefined,
  messages: [],
  isExpanded: true,
  childIds: [],
  isSelected: false,
  width: 450,
  height: 200,
  searchGrounding,
  hasQueried: false,
});

const createInitialMindMapData = (): MindMapData => {
  const rootNode = createInitialNode();
  return {
    nodes: { [rootNode.id]: rootNode },
    rootNodeId: rootNode.id,
    searchGrounding: false,
    selectedModel: DEFAULT_MODEL.id,
    version: 1,
    lastModified: Date.now(),
  };
};

const createInitialCanvas = (name: string = 'Canvas 1'): Canvas => {
  const now = Date.now();
  return {
    id: uuidv4(),
    name,
    data: createInitialMindMapData(),
    viewport: { x: 0, y: 0, zoom: 1 },
    createdAt: now,
    lastModified: now,
  };
};

const createInitialCanvasManagerData = (): CanvasManagerData => {
  const initialCanvas = createInitialCanvas();
  return {
    canvases: { [initialCanvas.id]: initialCanvas },
    activeCanvasId: initialCanvas.id,
    version: 1,
  };
};

// Migration function to convert old single-canvas data to new multi-canvas format
const migrateOldData = (): CanvasManagerData | null => {
  const oldData = localStorage.getItem('mindmap-data');
  if (!oldData) return null;

  try {
    const parsed = JSON.parse(oldData);
    if (parsed.nodes && parsed.rootNodeId) {
      // This is old format, migrate it
      const canvas: Canvas = {
        id: uuidv4(),
        name: 'Migrated Canvas',
        data: parsed,
        viewport: { x: 0, y: 0, zoom: 1 },
        createdAt: parsed.lastModified || Date.now(),
        lastModified: parsed.lastModified || Date.now(),
      };

      const canvasManagerData: CanvasManagerData = {
        canvases: { [canvas.id]: canvas },
        activeCanvasId: canvas.id,
        version: 1,
      };

      // Save the migrated data and remove old data
      localStorage.setItem(CANVAS_MANAGER_STORAGE_KEY, JSON.stringify(canvasManagerData));
      localStorage.removeItem('mindmap-data');
      
      return canvasManagerData;
    }
  } catch (error) {
    console.error('Failed to migrate old data:', error);
  }

  return null;
};

export const useCanvasManager = () => {
  const [canvasManagerData, setCanvasManagerData] = useState<CanvasManagerData>(() => {
    // Try to load existing canvas manager data
    const saved = localStorage.getItem(CANVAS_MANAGER_STORAGE_KEY);
    if (saved) {
      try {
        return JSON.parse(saved);
      } catch (error) {
        console.error('Failed to parse canvas manager data:', error);
      }
    }

    // Try to migrate old data
    const migrated = migrateOldData();
    if (migrated) {
      return migrated;
    }

    // Create fresh data
    return createInitialCanvasManagerData();
  });

  // Auto-save canvas manager data
  useEffect(() => {
    localStorage.setItem(CANVAS_MANAGER_STORAGE_KEY, JSON.stringify(canvasManagerData));
  }, [canvasManagerData]);

  const activeCanvas = canvasManagerData.canvases[canvasManagerData.activeCanvasId];

  const createCanvas = useCallback((name?: string) => {
    const canvasCount = Object.keys(canvasManagerData.canvases).length;
    const defaultName = name || `Canvas ${canvasCount + 1}`;
    const newCanvas = createInitialCanvas(defaultName);

    setCanvasManagerData(prev => ({
      ...prev,
      canvases: {
        ...prev.canvases,
        [newCanvas.id]: newCanvas,
      },
      activeCanvasId: newCanvas.id,
    }));

    return newCanvas.id;
  }, [canvasManagerData.canvases]);

  const switchCanvas = useCallback((canvasId: string) => {
    if (canvasManagerData.canvases[canvasId]) {
      setCanvasManagerData(prev => ({
        ...prev,
        activeCanvasId: canvasId,
      }));
    }
  }, [canvasManagerData.canvases]);

  const updateCanvas = useCallback((canvasId: string, updates: Partial<Pick<Canvas, 'name' | 'data' | 'viewport'>>) => {
    setCanvasManagerData(prev => ({
      ...prev,
      canvases: {
        ...prev.canvases,
        [canvasId]: {
          ...prev.canvases[canvasId],
          ...updates,
          lastModified: Date.now(),
        },
      },
    }));
  }, []);

  const deleteCanvas = useCallback((canvasId: string) => {
    const canvasIds = Object.keys(canvasManagerData.canvases);
    if (canvasIds.length <= 1) {
      // Don't delete the last canvas
      return false;
    }

    setCanvasManagerData(prev => {
      const newCanvases = { ...prev.canvases };
      delete newCanvases[canvasId];

      let newActiveCanvasId = prev.activeCanvasId;
      if (canvasId === prev.activeCanvasId) {
        // Switch to another canvas if we're deleting the active one
        const remainingIds = Object.keys(newCanvases);
        newActiveCanvasId = remainingIds[0];
      }

      return {
        ...prev,
        canvases: newCanvases,
        activeCanvasId: newActiveCanvasId,
      };
    });

    return true;
  }, [canvasManagerData.canvases]);

  const duplicateCanvas = useCallback((canvasId: string) => {
    const sourceCanvas = canvasManagerData.canvases[canvasId];
    if (!sourceCanvas) return null;

    const newCanvas: Canvas = {
      id: uuidv4(),
      name: `${sourceCanvas.name} (Copy)`,
      data: JSON.parse(JSON.stringify(sourceCanvas.data)), // Deep clone
      viewport: { ...sourceCanvas.viewport },
      createdAt: Date.now(),
      lastModified: Date.now(),
    };

    setCanvasManagerData(prev => ({
      ...prev,
      canvases: {
        ...prev.canvases,
        [newCanvas.id]: newCanvas,
      },
      activeCanvasId: newCanvas.id,
    }));

    return newCanvas.id;
  }, [canvasManagerData.canvases]);

  const renameCanvas = useCallback((canvasId: string, newName: string) => {
    updateCanvas(canvasId, { name: newName.trim() || 'Untitled Canvas' });
  }, [updateCanvas]);

  return {
    canvases: canvasManagerData.canvases,
    activeCanvasId: canvasManagerData.activeCanvasId,
    activeCanvas,
    createCanvas,
    switchCanvas,
    updateCanvas,
    deleteCanvas,
    duplicateCanvas,
    renameCanvas,
  };
};
